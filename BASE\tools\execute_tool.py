import json
import copy
from typing import Any
from logger.log import logger

# Import action modules
from BASE.actions.context_search import process_context_search
from BASE.actions.folder_search import process_folder_search
from BASE.actions.swagger_search import process_swagger_search
from BASE.actions.web_search import process_web_search
from BASE.actions.agentic_search import process_agentic_search

# Import PRO mode action modules
from BASE.actions.pro_mode.file_operations import (
    process_file_discovery, process_file_read, process_file_create,
    process_file_edit, process_file_manage
)
from BASE.actions.pro_mode.terminal_operations import (
    process_terminal_exec, process_process_monitor
)
from BASE.actions.pro_mode.environment_setup import process_environment_setup

from ipc import IPC

ipc_ = IPC.connect()


class SearchReferences:
    """Simple search references tracker for tool execution."""

    def __init__(self, request_id: str = ""):
        self.search_results = {"request_id": request_id, "results": []}

    def add_search_result(self, path: str, type: str, name: str, content: str):
        self.search_results["results"].append(
            {"path": path, "type": type, "name": name, "content": content}
        )

    def get_search_result(self):
        return copy.deepcopy(self.search_results)




async def execute_tool_call(tool_call: dict[str, Any]) -> tuple[dict[str, Any], bool]:
    """
    Execute a tool call and return the result.

    Args:
        tool_call: Tool call from LLM response

    Returns:
        Tuple of (tool_result, needs_followup)
    """
    function_name = tool_call["function"]["name"]
    function_args = json.loads(tool_call["function"]["arguments"])
    tool_id = tool_call["id"]

    logger.info(f"Executing tool call: {function_name} with args: {function_args}")

    try:
        # Create search references for this tool call
        search_references = SearchReferences(request_id=tool_id)

        # Route to appropriate action handler
        if function_name == "context_search":
            result, search_refs = await process_context_search(
                query=function_args["query"],
                tool_id=tool_id,
                kbid=function_args["kbid"],
                search_references=search_references,
            )

        elif function_name == "folder_search":
            result, search_refs = await process_folder_search(
                query=function_args["query"],
                tool_id=tool_id,
                folder_path=function_args["folder_path"],
                index_name=function_args["kbid"],
                search_references=search_references,
            )

        elif function_name == "web_search":
            result, search_refs = await process_web_search(
                query=function_args["query"],
                tool_id=tool_id,
                search_references=search_references,
            )

        elif function_name == "agentic_search":
            result, search_refs = await process_agentic_search(
                query=function_args["query"],
                tool_id=tool_id,
                kbid=function_args["kbid"],
                search_references=search_references,
            )

        elif function_name == "swagger_search":
            result, search_refs = await process_swagger_search(
                query=function_args["query"],
                tool_id=tool_id,
                kbid=function_args["kbid"],
                search_references=search_references,
            )

        # PRO Mode Tools
        elif function_name == "file_discovery":
            result, search_refs = await process_file_discovery(
                search_type=function_args["search_type"],
                query=function_args["query"],
                path=function_args.get("path", "."),
                recursive=function_args.get("recursive", True),
                include_hidden=function_args.get("include_hidden", False),
                tool_id=tool_id,
                search_references=search_references,
            )

        elif function_name == "file_read":
            result, search_refs = await process_file_read(
                file_paths=function_args["file_paths"],
                encoding=function_args.get("encoding", "utf-8"),
                line_range=function_args.get("line_range"),
                tool_id=tool_id,
                search_references=search_references,
            )

        elif function_name == "file_create":
            result, search_refs = await process_file_create(
                file_path=function_args["file_path"],
                content=function_args["content"],
                encoding=function_args.get("encoding", "utf-8"),
                create_dirs=function_args.get("create_dirs", False),
                overwrite=function_args.get("overwrite", False),
                tool_id=tool_id,
                search_references=search_references,
            )

        elif function_name == "file_edit":
            result, search_refs = await process_file_edit(
                file_path=function_args["file_path"],
                edit_type=function_args["edit_type"],
                content=function_args.get("content", ""),
                line_number=function_args.get("line_number"),
                line_range=function_args.get("line_range"),
                search_pattern=function_args.get("search_pattern"),
                backup=function_args.get("backup", True),
                tool_id=tool_id,
                search_references=search_references,
            )

        elif function_name == "file_manage":
            result, search_refs = await process_file_manage(
                operation=function_args["operation"],
                source_path=function_args["source_path"],
                destination_path=function_args.get("destination_path"),
                recursive=function_args.get("recursive", False),
                force=function_args.get("force", False),
                create_parents=function_args.get("create_parents", False),
                tool_id=tool_id,
                search_references=search_references,
            )

        elif function_name == "terminal_exec":
            result, search_refs = await process_terminal_exec(
                command=function_args["command"],
                working_directory=function_args.get("working_directory"),
                timeout=function_args.get("timeout", 30),
                capture_output=function_args.get("capture_output", True),
                shell=function_args.get("shell", True),
                env_vars=function_args.get("env_vars"),
                tool_id=tool_id,
                search_references=search_references,
            )

        elif function_name == "process_monitor":
            result, search_refs = await process_process_monitor(
                action=function_args["action"],
                process_name=function_args.get("process_name"),
                command=function_args.get("command"),
                working_directory=function_args.get("working_directory"),
                process_id=function_args.get("process_id"),
                signal_name=function_args.get("signal", "SIGTERM"),
                tool_id=tool_id,
                search_references=search_references,
            )

        elif function_name == "environment_setup":
            result, search_refs = await process_environment_setup(
                setup_type=function_args["setup_type"],
                language=function_args.get("language", "auto"),
                project_path=function_args.get("project_path"),
                package_manager=function_args.get("package_manager"),
                requirements_file=function_args.get("requirements_file"),
                virtual_env_name=function_args.get("virtual_env_name"),
                tool_id=tool_id,
                search_references=search_references,
            )

        else:
            raise ValueError(f"Unknown tool function: {function_name}")

        # Format tool result for LLM
        tool_result = {
            "tool_call_id": tool_id,
            "content": json.dumps(result)
        }
        return tool_result, False  # Success, no follow-up needed
    
    except Exception as e:
        logger.error(f"Error executing tool call {function_name}: {e}")

        error_result = {
            "tool_call_id": tool_id,
            "content": json.dumps({
                "error": str(e),
                "status": "error"
            })
        }
        return error_result, True  # Error requires follow-up
